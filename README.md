# shuangchuan.m 项目逻辑框架分析工具

这是一个用于分析和可视化 `shuangchuan.m` 海上风电机组安装调度系统的工具。

## 功能特性

- 🎨 **matplotlib 流程图生成**: 生成高质量的系统流程图
- 📊 **Mermaid 代码输出**: 提供完整的 Mermaid 流程图代码
- 📖 **系统模块说明**: 详细的系统组件和算法特点说明
- 🖥️ **交互式菜单**: 用户友好的命令行界面

## 安装要求

```bash
pip install matplotlib numpy
```

## 使用方法

### 1. 命令行参数

```bash
# 生成 matplotlib 流程图
python p.py --flowchart

# 显示 Mermaid 代码（简洁版）
python p.py --mermaid-simple

# 显示 Mermaid 代码（详细版）
python p.py --mermaid

# 显示系统模块说明
python p.py --info

# 显示帮助信息
python p.py --help
```

### 2. 交互式菜单

```bash
# 启动交互式菜单
python p.py
```

然后根据提示选择功能：
1. 生成matplotlib流程图
2. 显示Mermaid代码
3. 显示系统模块说明
4. 退出

## 输出文件

- `flowchart.png`: 生成的高分辨率流程图（300 DPI）

## 系统架构

### 核心算法模块
- **系统初始化**: 设置环境参数和基础配置
- **问题参数设置**: 定义调度问题的具体参数
- **数据结构定义**: 创建必要的数据结构支持算法运行
- **混合GA-PSO初始调度**: 结合遗传算法和粒子群优化的初始调度方案
- **种群初始化**: 为优化算法创建初始种群
- **适应度评估**: 评估每个解决方案的质量
- **多样性检测**: 检查种群多样性水平
- **PSO局部搜索**: 使用粒子群算法进行局部搜索（当多样性低时）
- **GA操作**: 使用遗传算法操作（当多样性高时）
- **更新最优解**: 记录当前最佳解决方案
- **达到终止条件?**: 检查是否满足终止条件
- **输出最优调度**: 返回最佳的调度方案

### 重调度模块
- **故障模拟**: 模拟系统故障场景
- **完全重调度**: 在故障后重新执行调度过程
- **识别已完成任务**: 确认在故障前已完成的任务
- **处理正在进行任务**: 处理中断的任务状态
- **提取未完成任务**: 找出尚未开始或未完成的任务
- **重新优化调度**: 优化剩余任务的调度方案
- **合并调度结果**: 将新旧调度方案合并
- **结果可视化**: 展示最终的重调度结果

## 算法特点

- **混合优化策略**: 结合遗传算法(GA)的全局搜索能力和粒子群优化(PSO)的局部搜索能力
- **动态适应性**: 通过多样性检测动态切换优化策略
- **故障恢复机制**: 内置完整故障处理流程，保证系统健壮性
- **并行处理**: 核心算法和重调度模块可并行运行，提高效率

## Mermaid 图表使用

生成的 Mermaid 代码可以在以下平台使用：

1. **在线编辑器**: [Mermaid Live Editor](https://mermaid.live/)
2. **GitHub**: 直接在 README.md 中使用
3. **VS Code**: 安装 Mermaid 插件
4. **其他 Markdown 编辑器**: 大多数现代编辑器都支持

## 文件说明

- `p.py`: 主程序文件
- `mermaid_flowcharts.py`: 独立的 Mermaid 代码生成器
- `shuangchuan.m`: 原始 MATLAB 调度系统代码
- `flowchart.png`: 生成的流程图

## 技术栈

- **Python 3.x**
- **matplotlib**: 图形绘制
- **numpy**: 数值计算
- **Mermaid**: 流程图代码生成

## 许可证

本项目仅用于学术研究和教育目的。

---

**注意**: 确保您的系统已安装 Python 3.x 和必要的依赖包。如果遇到字体问题，程序会自动使用英文标签以确保兼容性。
