import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# 设置全局字体 - 使用系统默认字体避免字体问题
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# ==================== Mermaid 流程图代码 ====================

def get_overall_architecture():
    """总体逻辑架构图"""
    return """
graph TD
    A[系统初始化] --> B[问题参数设置]
    B --> C[数据结构定义]
    C --> D[混合GA-PSO初始调度]
    D --> E[故障模拟]
    E --> F[完全重调度]
    F --> G[结果可视化]

    subgraph "核心算法模块"
        D --> D1[种群初始化]
        D1 --> D2[适应度评估]
        D2 --> D3[多样性检测]
        D3 --> D4{多样性低?}
        D4 -->|是| D5[PSO局部搜索]
        D4 -->|否| D6[GA操作]
        D5 --> D7[更新最优解]
        D6 --> D7
        D7 --> D8{达到终止条件?}
        D8 -->|否| D2
        D8 -->|是| D9[输出最优调度]
    end

    subgraph "重调度模块"
        F --> F1[识别已完成任务]
        F1 --> F2[处理正在进行任务]
        F2 --> F3[提取未完成任务]
        F3 --> F4[重新优化调度]
        F4 --> F5[合并调度结果]
    end
"""

def get_hybrid_ga_pso_algorithm():
    """混合GA-PSO算法详细流程图"""
    return """
flowchart TD
    A[开始] --> B[初始化参数]
    B --> C[生成初始种群]
    C --> D[初始化PSO参数]
    D --> E[开始进化循环]

    E --> F[评估种群适应度]
    F --> G[更新个体最优pbest]
    G --> H[更新全局最优gbest]
    H --> I[计算种群多样性]

    I --> J{多样性 < 阈值?}

    J -->|是| K[执行PSO操作]
    K --> K1[更新粒子速度]
    K1 --> K2[更新粒子位置]
    K2 --> K3[PSO变异操作]
    K3 --> K4[评估新解]
    K4 --> K5[更新pbest和gbest]
    K5 --> L[检查终止条件]

    J -->|否| M[执行GA操作]
    M --> M1[精英选择]
    M1 --> M2[锦标赛选择]
    M2 --> M3[交叉操作OX]
    M3 --> M4[变异操作]
    M4 --> M5[生成新种群]
    M5 --> L

    L --> N{达到最大代数?}
    N -->|否| F
    N -->|是| O[输出最优解]
    O --> P[解码为调度方案]
    P --> Q[结束]
"""

def get_chromosome_decoding():
    """染色体解码为调度方案算法图"""
    return """
flowchart TD
    A[输入染色体] --> B[提取涡轮机顺序和船舶分配]
    B --> C[初始化船舶和泊位可用时间]
    C --> D[开始处理涡轮机]

    D --> E[获取当前涡轮机和分配船舶]
    E --> F[获取船舶属性和海况]
    F --> G{船舶类型?}

    G -->|单体船| H1[组装级别=0]
    G -->|双体船| H2[组装级别=2]

    H1 --> I[分配泊位]
    H2 --> I

    I --> J[计算装载时间]
    J --> K{需要船上组装?}

    K -->|是| L[添加船上组装任务]
    K -->|否| M[计算前往风场时间]
    L --> M

    M --> N[添加航行任务]
    N --> O{组装级别?}

    O -->|0-无组装| P[执行所有常规工序]
    O -->|2-完全组装| Q[执行最终安装工序]

    P --> R[应用海况影响]
    Q --> R

    R --> S[添加返回港口任务]
    S --> T[更新船舶可用时间]
    T --> U{还有涡轮机?}

    U -->|是| D
    U -->|否| V[输出完整调度]
"""

def get_data_structure_diagram():
    """核心数据结构关系图"""
    return """
erDiagram
    TURBINES {
        int id
        array processes
        int assembly_level
        struct ship_assembly_time
        int sea_condition_index
        float preassembly_time
        bool is_preassembled
    }

    VESSELS {
        int id
        int type
        float speed
        int capacity
        float loading_time
        float deck_space
        float stability
        float sea_efficiency
        int max_assembly_level
    }

    BERTHS {
        int id
    }

    SCHEDULE {
        array turbine_id
        array vessel_id
        array start_time
        array end_time
        array process_id
        array berth_id
        array sea_condition
        array assembly_level
    }

    SEA_CONDITIONS {
        array wave_height
        array wind_speed
        array current_speed
    }

    CHROMOSOME {
        array turbine_order
        array vessel_assignment
    }

    TURBINES ||--o{ SCHEDULE : "安装任务"
    VESSELS ||--o{ SCHEDULE : "执行任务"
    BERTHS ||--o{ SCHEDULE : "占用泊位"
    SEA_CONDITIONS ||--o{ TURBINES : "影响位置"
    CHROMOSOME ||--|| SCHEDULE : "解码生成"
"""

def get_rescheduling_algorithm():
    """完全重调度算法流程图"""
    return """
flowchart TD
    A[故障发生] --> B[输入原始调度和故障信息]
    B --> C[分析任务状态]

    C --> D[识别已完成任务]
    C --> E[识别正在进行任务]
    C --> F[识别未开始任务]

    D --> G[保留已完成任务]
    E --> H[调整正在进行任务]
    F --> I[提取未完成涡轮机]

    G --> J[创建新调度基础]
    H --> K[添加维修任务]
    K --> L[处理故障船舶延迟]

    I --> M[构建简化问题]
    M --> N[计算船舶可用时间]
    N --> O[调用优化算法]

    O --> P[生成剩余任务调度]

    J --> Q[合并调度结果]
    L --> Q
    P --> Q

    Q --> R[输出完整重调度方案]

    subgraph "任务状态分类"
        C1[end_time ≤ 故障时间] --> D
        C2[start_time < 故障时间 < end_time] --> E
        C3[start_time ≥ 故障时间] --> F
    end
"""

def print_all_mermaid_diagrams():
    """打印所有Mermaid流程图代码"""
    print("=" * 80)
    print("shuangchuan.m 项目逻辑框架 - Mermaid 流程图代码")
    print("=" * 80)

    print("\n1. 总体逻辑架构图:")
    print("-" * 50)
    print(get_overall_architecture())

    print("\n2. 混合GA-PSO算法详细流程图:")
    print("-" * 50)
    print(get_hybrid_ga_pso_algorithm())

    print("\n3. 染色体解码为调度方案算法图:")
    print("-" * 50)
    print(get_chromosome_decoding())

    print("\n4. 核心数据结构关系图:")
    print("-" * 50)
    print(get_data_structure_diagram())

    print("\n5. 完全重调度算法流程图:")
    print("-" * 50)
    print(get_rescheduling_algorithm())

    print("\n" + "=" * 80)
    print("使用说明:")
    print("1. 复制上述Mermaid代码到支持Mermaid的编辑器中")
    print("2. 或者使用在线Mermaid编辑器: https://mermaid.live/")
    print("3. 或者在Markdown文档中使用 ```mermaid 代码块")
    print("=" * 80)

# ==================== 纯 matplotlib 流程图代码 ====================

def draw_flowchart():
    """使用纯matplotlib绘制流程图"""
    print("Generating Hybrid GA-PSO Scheduling System Flowchart...")

    # 创建图形
    fig, ax = plt.subplots(figsize=(16, 12))

    # 定义节点位置和信息 (使用英文避免字体问题)
    nodes = {
        # 主流程
        "System Init": (8, 11, 'rect', 'lightblue'),
        "Parameter Setup": (8, 10, 'rect', 'lightblue'),
        "Data Structure": (8, 9, 'rect', 'lightblue'),
        "Hybrid GA-PSO": (8, 8, 'rect', 'lightgreen'),
        "Fault Simulation": (8, 7, 'rect', 'orange'),
        "Complete Reschedule": (8, 6, 'rect', 'orange'),
        "Result Visualization": (8, 5, 'rect', 'lightcoral'),

        # 重调度模块
        "Identify Completed": (3, 6, 'rect', 'lightyellow'),
        "Handle Ongoing": (3, 5.5, 'rect', 'lightyellow'),
        "Extract Remaining": (3, 5, 'rect', 'lightyellow'),
        "Re-optimize": (3, 4.5, 'rect', 'lightyellow'),
        "Merge Results": (3, 4, 'rect', 'lightyellow'),

        # 核心算法模块
        "Population Init": (13, 8, 'rect', 'lightsteelblue'),
        "Fitness Eval": (13, 7.5, 'rect', 'lightsteelblue'),
        "Diversity Check": (13, 7, 'diamond', 'yellow'),
        "PSO Search": (11, 6.5, 'rect', 'lightsteelblue'),
        "GA Operations": (15, 6.5, 'rect', 'lightsteelblue'),
        "Update Best": (13, 6, 'rect', 'lightsteelblue'),
        "Termination?": (13, 5.5, 'diamond', 'yellow'),
        "Output Schedule": (13, 5, 'rect', 'lightsteelblue'),
    }

    # 定义连接关系
    connections = [
        ("System Init", "Parameter Setup"),
        ("Parameter Setup", "Data Structure"),
        ("Data Structure", "Hybrid GA-PSO"),
        ("Hybrid GA-PSO", "Fault Simulation"),
        ("Hybrid GA-PSO", "Population Init"),
        ("Fault Simulation", "Complete Reschedule"),
        ("Complete Reschedule", "Identify Completed"),
        ("Complete Reschedule", "Result Visualization"),
        ("Identify Completed", "Handle Ongoing"),
        ("Handle Ongoing", "Extract Remaining"),
        ("Extract Remaining", "Re-optimize"),
        ("Re-optimize", "Merge Results"),
        ("Merge Results", "Result Visualization"),
        ("Population Init", "Fitness Eval"),
        ("Fitness Eval", "Diversity Check"),
        ("Diversity Check", "PSO Search"),
        ("Diversity Check", "GA Operations"),
        ("PSO Search", "Update Best"),
        ("GA Operations", "Update Best"),
        ("Update Best", "Termination?"),
        ("Termination?", "Output Schedule"),
        ("Termination?", "Fitness Eval"),  # 循环
    ]

    # 绘制模块背景
    # 重调度模块背景
    rect1 = patches.Rectangle((1.5, 3.5), 3, 3, linewidth=2,
                             edgecolor='blue', facecolor='lightblue', alpha=0.2)
    ax.add_patch(rect1)
    ax.text(3, 6.8, 'Rescheduling Module', fontsize=12, fontweight='bold',
            ha='center', color='navy')

    # 核心算法模块背景
    rect2 = patches.Rectangle((10.5, 4.5), 5, 4, linewidth=2,
                             edgecolor='green', facecolor='lightgreen', alpha=0.2)
    ax.add_patch(rect2)
    ax.text(13, 8.8, 'Core Algorithm Module', fontsize=12, fontweight='bold',
            ha='center', color='darkgreen')

    # 绘制节点
    for node_name, (x, y, shape, color) in nodes.items():
        if shape == 'diamond':
            # 绘制菱形
            diamond = patches.RegularPolygon((x, y), 4, radius=0.3,
                                           orientation=np.pi/4,
                                           facecolor=color, edgecolor='black')
            ax.add_patch(diamond)
        else:
            # 绘制矩形
            rect = FancyBboxPatch((x-0.4, y-0.15), 0.8, 0.3,
                                boxstyle="round,pad=0.02",
                                facecolor=color, edgecolor='black')
            ax.add_patch(rect)

        # 添加文本标签
        if len(node_name) > 12:
            # 长文本换行
            lines = []
            if "GA-PSO" in node_name:
                lines = ["Hybrid", "GA-PSO"]
            elif "?" in node_name:
                lines = [node_name.replace("?", ""), "?"]
            elif "Complete Reschedule" in node_name:
                lines = ["Complete", "Reschedule"]
            elif "Result Visualization" in node_name:
                lines = ["Result", "Visualization"]
            elif "Core Algorithm Module" in node_name:
                lines = ["Core Algorithm", "Module"]
            else:
                # 智能分割长单词
                words = node_name.split()
                if len(words) > 1:
                    mid = len(words) // 2
                    lines = [" ".join(words[:mid]), " ".join(words[mid:])]
                else:
                    mid = len(node_name) // 2
                    lines = [node_name[:mid], node_name[mid:]]

            for i, line in enumerate(lines):
                ax.text(x, y + 0.05 - i*0.1, line, ha='center', va='center',
                       fontsize=8, fontweight='bold')
        else:
            ax.text(x, y, node_name, ha='center', va='center',
                   fontsize=9, fontweight='bold')

    # 绘制连接线
    for start, end in connections:
        if start in nodes and end in nodes:
            x1, y1 = nodes[start][:2]
            x2, y2 = nodes[end][:2]

            # 绘制箭头
            ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                       arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))

    # 添加决策节点的标签
    ax.text(12, 6.8, 'Yes', fontsize=10, color='red', fontweight='bold')
    ax.text(14, 6.8, 'No', fontsize=10, color='red', fontweight='bold')
    ax.text(13.5, 5.2, 'Yes', fontsize=10, color='red', fontweight='bold')
    ax.text(12.5, 5.8, 'No', fontsize=10, color='red', fontweight='bold')

    # 设置图形属性
    ax.set_xlim(0, 16)
    ax.set_ylim(3, 12)
    ax.set_aspect('equal')
    ax.axis('off')

    # 添加标题
    plt.title('Hybrid GA-PSO Offshore Wind Turbine Scheduling System', fontsize=16, fontweight='bold', pad=20)

    # 保存和显示图形
    plt.tight_layout()
    plt.savefig('flowchart.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Flowchart saved as flowchart.png")

def show_system_info():
    """显示系统模块说明"""
    print("\n" + "="*80)
    print("系统模块说明")
    print("="*80)

    print("\n### 核心算法模块")
    print("- 系统初始化：设置环境参数和基础配置")
    print("- 问题参数设置：定义调度问题的具体参数")
    print("- 数据结构定义：创建必要的数据结构支持算法运行")
    print("- 混合GA-PSO初始调度：结合遗传算法和粒子群优化的初始调度方案")
    print("- 种群初始化：为优化算法创建初始种群")
    print("- 适应度评估：评估每个解决方案的质量")
    print("- 多样性检测：检查种群多样性水平")
    print("- PSO局部搜索：使用粒子群算法进行局部搜索（当多样性低时）")
    print("- GA操作：使用遗传算法操作（当多样性高时）")
    print("- 更新最优解：记录当前最佳解决方案")
    print("- 达到终止条件?：检查是否满足终止条件")
    print("- 输出最优调度：返回最佳的调度方案")

    print("\n### 重调度模块")
    print("- 故障模拟：模拟系统故障场景")
    print("- 完全重调度：在故障后重新执行调度过程")
    print("- 识别已完成任务：确认在故障前已完成的任务")
    print("- 处理正在进行任务：处理中断的任务状态")
    print("- 提取未完成任务：找出尚未开始或未完成的任务")
    print("- 重新优化调度：优化剩余任务的调度方案")
    print("- 合并调度结果：将新旧调度方案合并")
    print("- 结果可视化：展示最终的重调度结果")

    print("\n### 算法特点")
    print("- 混合优化策略：结合遗传算法(GA)的全局搜索能力和粒子群优化(PSO)的局部搜索能力")
    print("- 动态适应性：通过多样性检测动态切换优化策略")
    print("- 故障恢复机制：内置完整故障处理流程，保证系统健壮性")
    print("- 并行处理：核心算法和重调度模块可并行运行，提高效率")

def show_mermaid_codes():
    """显示所有Mermaid流程图代码"""
    print("\n" + "="*80)
    print("Mermaid 流程图代码")
    print("="*80)
    print("以下是 shuangchuan.m 项目的各种流程图的 Mermaid 代码")
    print("您可以复制这些代码到支持 Mermaid 的编辑器中使用")

    print("\n### 1. 总体逻辑架构图")
    print("-" * 50)
    print(get_overall_architecture())

    print("\n### 2. 混合GA-PSO算法详细流程图")
    print("-" * 50)
    print(get_hybrid_ga_pso_algorithm())

    print("\n### 3. 染色体解码为调度方案算法图")
    print("-" * 50)
    print(get_chromosome_decoding())

    print("\n### 4. 核心数据结构关系图")
    print("-" * 50)
    print(get_data_structure_diagram())

    print("\n### 5. 完全重调度算法流程图")
    print("-" * 50)
    print(get_rescheduling_algorithm())

    print("\n### 使用说明")
    print("1. 在线编辑器: 复制上述代码到 https://mermaid.live/ 中查看图形")
    print("2. Markdown文档: 在Markdown文档中使用 ```mermaid 代码块包围代码")
    print("3. GitHub: GitHub原生支持Mermaid图表，直接在README.md中使用即可")
    print("4. VS Code: 安装Mermaid插件后可以预览图表")
    print("5. 其他工具: 大多数现代文档工具都支持Mermaid图表")

# ==================== 主程序 ====================

def main():
    """主函数"""
    import sys

    print("="*80)
    print("shuangchuan.m 项目逻辑框架分析工具")
    print("="*80)

    if len(sys.argv) > 1:
        if sys.argv[1] == "--mermaid":
            print_all_mermaid_diagrams()
        elif sys.argv[1] == "--info":
            show_system_info()
        elif sys.argv[1] == "--mermaid-simple":
            show_mermaid_codes()
        elif sys.argv[1] == "--flowchart":
            draw_flowchart()
        else:
            print("未知参数:", sys.argv[1])
            show_help()
    else:
        # 默认显示菜单
        show_menu()

def show_help():
    """显示帮助信息"""
    print("\n使用方法:")
    print("python p.py [选项]")
    print("\n选项:")
    print("  --flowchart     生成并显示matplotlib流程图")
    print("  --mermaid       输出所有Mermaid代码（详细版）")
    print("  --mermaid-simple 输出所有Mermaid代码（简洁版）")
    print("  --info          显示系统模块说明")
    print("  无参数           显示交互式菜单")

def show_menu():
    """显示交互式菜单"""
    while True:
        print("\n请选择功能:")
        print("1. 生成matplotlib流程图")
        print("2. 显示Mermaid代码")
        print("3. 显示系统模块说明")
        print("4. 退出")

        try:
            choice = input("\n请输入选择 (1-4): ").strip()

            if choice == '1':
                draw_flowchart()
            elif choice == '2':
                show_mermaid_codes()
            elif choice == '3':
                show_system_info()
            elif choice == '4':
                print("感谢使用！")
                break
            else:
                print("无效选择，请输入1-4之间的数字")

        except KeyboardInterrupt:
            print("\n\n程序已退出")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()