import streamlit as st
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.patches import BoxStyle, Wedge
import matplotlib as mpl

# 设置全局字体
mpl.rcParams['font.family'] = 'SimHei'  # 使用黑体显示中文
mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# ==================== Mermaid 流程图代码 ====================

def get_overall_architecture():
    """总体逻辑架构图"""
    return """
graph TD
    A[系统初始化] --> B[问题参数设置]
    B --> C[数据结构定义]
    C --> D[混合GA-PSO初始调度]
    D --> E[故障模拟]
    E --> F[完全重调度]
    F --> G[结果可视化]

    subgraph "核心算法模块"
        D --> D1[种群初始化]
        D1 --> D2[适应度评估]
        D2 --> D3[多样性检测]
        D3 --> D4{多样性低?}
        D4 -->|是| D5[PSO局部搜索]
        D4 -->|否| D6[GA操作]
        D5 --> D7[更新最优解]
        D6 --> D7
        D7 --> D8{达到终止条件?}
        D8 -->|否| D2
        D8 -->|是| D9[输出最优调度]
    end

    subgraph "重调度模块"
        F --> F1[识别已完成任务]
        F1 --> F2[处理正在进行任务]
        F2 --> F3[提取未完成任务]
        F3 --> F4[重新优化调度]
        F4 --> F5[合并调度结果]
    end
"""

def get_hybrid_ga_pso_algorithm():
    """混合GA-PSO算法详细流程图"""
    return """
flowchart TD
    A[开始] --> B[初始化参数]
    B --> C[生成初始种群]
    C --> D[初始化PSO参数]
    D --> E[开始进化循环]

    E --> F[评估种群适应度]
    F --> G[更新个体最优pbest]
    G --> H[更新全局最优gbest]
    H --> I[计算种群多样性]

    I --> J{多样性 < 阈值?}

    J -->|是| K[执行PSO操作]
    K --> K1[更新粒子速度]
    K1 --> K2[更新粒子位置]
    K2 --> K3[PSO变异操作]
    K3 --> K4[评估新解]
    K4 --> K5[更新pbest和gbest]
    K5 --> L[检查终止条件]

    J -->|否| M[执行GA操作]
    M --> M1[精英选择]
    M1 --> M2[锦标赛选择]
    M2 --> M3[交叉操作OX]
    M3 --> M4[变异操作]
    M4 --> M5[生成新种群]
    M5 --> L

    L --> N{达到最大代数?}
    N -->|否| F
    N -->|是| O[输出最优解]
    O --> P[解码为调度方案]
    P --> Q[结束]
"""

def get_chromosome_decoding():
    """染色体解码为调度方案算法图"""
    return """
flowchart TD
    A[输入染色体] --> B[提取涡轮机顺序和船舶分配]
    B --> C[初始化船舶和泊位可用时间]
    C --> D[开始处理涡轮机]

    D --> E[获取当前涡轮机和分配船舶]
    E --> F[获取船舶属性和海况]
    F --> G{船舶类型?}

    G -->|单体船| H1[组装级别=0]
    G -->|双体船| H2[组装级别=2]

    H1 --> I[分配泊位]
    H2 --> I

    I --> J[计算装载时间]
    J --> K{需要船上组装?}

    K -->|是| L[添加船上组装任务]
    K -->|否| M[计算前往风场时间]
    L --> M

    M --> N[添加航行任务]
    N --> O{组装级别?}

    O -->|0-无组装| P[执行所有常规工序]
    O -->|2-完全组装| Q[执行最终安装工序]

    P --> R[应用海况影响]
    Q --> R

    R --> S[添加返回港口任务]
    S --> T[更新船舶可用时间]
    T --> U{还有涡轮机?}

    U -->|是| D
    U -->|否| V[输出完整调度]
"""

def get_data_structure_diagram():
    """核心数据结构关系图"""
    return """
erDiagram
    TURBINES {
        int id
        array processes
        int assembly_level
        struct ship_assembly_time
        int sea_condition_index
        float preassembly_time
        bool is_preassembled
    }

    VESSELS {
        int id
        int type
        float speed
        int capacity
        float loading_time
        float deck_space
        float stability
        float sea_efficiency
        int max_assembly_level
    }

    BERTHS {
        int id
    }

    SCHEDULE {
        array turbine_id
        array vessel_id
        array start_time
        array end_time
        array process_id
        array berth_id
        array sea_condition
        array assembly_level
    }

    SEA_CONDITIONS {
        array wave_height
        array wind_speed
        array current_speed
    }

    CHROMOSOME {
        array turbine_order
        array vessel_assignment
    }

    TURBINES ||--o{ SCHEDULE : "安装任务"
    VESSELS ||--o{ SCHEDULE : "执行任务"
    BERTHS ||--o{ SCHEDULE : "占用泊位"
    SEA_CONDITIONS ||--o{ TURBINES : "影响位置"
    CHROMOSOME ||--|| SCHEDULE : "解码生成"
"""

def get_rescheduling_algorithm():
    """完全重调度算法流程图"""
    return """
flowchart TD
    A[故障发生] --> B[输入原始调度和故障信息]
    B --> C[分析任务状态]

    C --> D[识别已完成任务]
    C --> E[识别正在进行任务]
    C --> F[识别未开始任务]

    D --> G[保留已完成任务]
    E --> H[调整正在进行任务]
    F --> I[提取未完成涡轮机]

    G --> J[创建新调度基础]
    H --> K[添加维修任务]
    K --> L[处理故障船舶延迟]

    I --> M[构建简化问题]
    M --> N[计算船舶可用时间]
    N --> O[调用优化算法]

    O --> P[生成剩余任务调度]

    J --> Q[合并调度结果]
    L --> Q
    P --> Q

    Q --> R[输出完整重调度方案]

    subgraph "任务状态分类"
        C1[end_time ≤ 故障时间] --> D
        C2[start_time < 故障时间 < end_time] --> E
        C3[start_time ≥ 故障时间] --> F
    end
"""

def print_all_mermaid_diagrams():
    """打印所有Mermaid流程图代码"""
    print("=" * 80)
    print("shuangchuan.m 项目逻辑框架 - Mermaid 流程图代码")
    print("=" * 80)

    print("\n1. 总体逻辑架构图:")
    print("-" * 50)
    print(get_overall_architecture())

    print("\n2. 混合GA-PSO算法详细流程图:")
    print("-" * 50)
    print(get_hybrid_ga_pso_algorithm())

    print("\n3. 染色体解码为调度方案算法图:")
    print("-" * 50)
    print(get_chromosome_decoding())

    print("\n4. 核心数据结构关系图:")
    print("-" * 50)
    print(get_data_structure_diagram())

    print("\n5. 完全重调度算法流程图:")
    print("-" * 50)
    print(get_rescheduling_algorithm())

    print("\n" + "=" * 80)
    print("使用说明:")
    print("1. 复制上述Mermaid代码到支持Mermaid的编辑器中")
    print("2. 或者使用在线Mermaid编辑器: https://mermaid.live/")
    print("3. 或者在Markdown文档中使用 ```mermaid 代码块")
    print("=" * 80)

# ==================== 原有的 NetworkX 流程图代码 ====================

# 定义流程图节点
def draw_flowchart():
    st.title("混合GA-PSO调度系统流程图")
    
    # 创建有向图
    G = nx.DiGraph()
    
    # 添加节点（按流程顺序）
    nodes = [
        "系统初始化", "问题参数设置", "数据结构定义", "混合GA-PSO初始调度",
        "故障模拟", "完全重调度", "识别已完成任务", "处理正在进行任务", 
        "提取未完成任务", "重新优化调度", "合并调度结果", "结果可视化",
        "种群初始化", "适应度评估", "多样性检测", "PSO局部搜索", 
        "GA操作", "更新最优解", "达到终止条件?", "输出最优调度"
    ]
    
    # 添加节点到图中
    for node in nodes:
        G.add_node(node)
    
    # 添加边（节点关系）
    edges = [
        ("系统初始化", "问题参数设置"),
        ("问题参数设置", "数据结构定义"),
        ("数据结构定义", "混合GA-PSO初始调度"),
        ("混合GA-PSO初始调度", "故障模拟"),
        ("混合GA-PSO初始调度", "种群初始化"),
        ("故障模拟", "完全重调度"),
        ("完全重调度", "识别已完成任务"),
        ("识别已完成任务", "处理正在进行任务"),
        ("处理正在进行任务", "提取未完成任务"),
        ("提取未完成任务", "重新优化调度"),
        ("重新优化调度", "合并调度结果"),
        ("合并调度结果", "结果可视化"),
        ("种群初始化", "适应度评估"),
        ("适应度评估", "多样性检测"),
        ("多样性检测", "PSO局部搜索"),
        ("多样性检测", "GA操作"),
        ("PSO局部搜索", "更新最优解"),
        ("GA操作", "更新最优解"),
        ("更新最优解", "达到终止条件?"),
        ("达到终止条件?", "输出最优调度"),
        ("达到终止条件?", "适应度评估")  # 循环连接
    ]
    
    for edge in edges:
        G.add_edge(*edge)
    
    # 创建图形布局
    plt.figure(figsize=(16, 18))
    
    # 定义自定义布局
    pos = {
        "系统初始化": (7, 15), 
        "问题参数设置": (7, 13),
        "数据结构定义": (7, 11),
        "混合GA-PSO初始调度": (7, 9),
        
        # 重调度模块
        "故障模拟": (1.5, 7),
        "完全重调度": (1.5, 5),
        "识别已完成任务": (1.5, 3),
        "处理正在进行任务": (1.5, 1),
        "提取未完成任务": (1.5, -1),
        "重新优化调度": (1.5, -3),
        "合并调度结果": (1.5, -5),
        "结果可视化": (4, -7),
        
        # 核心算法模块
        "种群初始化": (10.5, 7),
        "适应度评估": (10.5, 5),
        "多样性检测": (10.5, 3),
        "PSO局部搜索": (7.5, 1),
        "GA操作": (13.5, 1),
        "更新最优解": (10.5, -1),
        "达到终止条件?": (10.5, -3),
        "输出最优调度": (10.5, -7),
    }
    
    # 节点形状映射
    node_shapes = {
        "多样性检测": "diamond",
        "达到终止条件?": "diamond"
    }
    
    # 手动添加集群边界
    cluster1_poly = plt.Polygon([[0, 8], [0, -7.5], [3, -7.5], [3, 8]], 
                                facecolor='lightblue', alpha=0.1)
    cluster2_poly = plt.Polygon([[4.5, 10], [4.5, -10], [15, -10], [15, 10]], 
                                facecolor='lightgreen', alpha=0.1)
    
    # 创建图
    fig, ax = plt.subplots()
    ax.add_patch(cluster1_poly)
    ax.add_patch(cluster2_poly)
    
    # 添加集群标签
    plt.text(1.5, 8.5, "重调度模块", fontsize=12, weight='bold', color='navy')
    plt.text(10, 11, "核心算法模块", fontsize=12, weight='bold', color='darkgreen')
    
    # 绘制节点
    for node in G.nodes:
        shape = node_shapes.get(node, "s")  # 默认为矩形
        nx.draw_networkx_nodes(G, pos, nodelist=[node], node_shape=shape, 
                              node_size=3200, node_color='lightblue', edgecolors='black', alpha=0.8)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, width=1.5, arrowstyle='-|>', arrowsize=20)
    
    # 添加边标签
    edge_labels = {
        ("多样性检测", "PSO局部搜索"): "是",
        ("多样性检测", "GA操作"): "否",
        ("达到终止条件?", "输出最优调度"): "是",
        ("达到终止条件?", "适应度评估"): "否"
    }
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)
    
    # 绘制节点标签
    labels = {}
    for node in G.nodes:
        # 根据节点名称确定换行位置
        if "GA-PSO" in node:
            labels[node] = "混合GA-PSO\n初始调度"
        elif "完全重调度" == node:
            labels[node] = node
        else:
            labels[node] = node.replace("任务", "任务\n").replace("调度", "调度\n").replace("算法", "算法\n")
    
    # 绘制节点标签
    nx.draw_networkx_labels(G, pos, labels, font_size=8.5)
    
    # 移除坐标轴
    plt.axis('off')
    
    # 显示图形
    st.pyplot(fig)

# 添加选项卡来选择不同的显示方式
tab1, tab2 = st.tabs(["NetworkX 流程图", "Mermaid 流程图代码"])

with tab1:
    # 显示NetworkX流程图
    draw_flowchart()

with tab2:
    # 显示Mermaid流程图代码
    st.markdown("## Mermaid 流程图代码")
    st.markdown("以下是 shuangchuan.m 项目的各种流程图的 Mermaid 代码，您可以复制这些代码到支持 Mermaid 的编辑器中使用。")

    # 1. 总体逻辑架构图
    st.markdown("### 1. 总体逻辑架构图")
    st.code(get_overall_architecture(), language='text')

    # 2. 混合GA-PSO算法详细流程图
    st.markdown("### 2. 混合GA-PSO算法详细流程图")
    st.code(get_hybrid_ga_pso_algorithm(), language='text')

    # 3. 染色体解码算法图
    st.markdown("### 3. 染色体解码为调度方案算法图")
    st.code(get_chromosome_decoding(), language='text')

    # 4. 数据结构关系图
    st.markdown("### 4. 核心数据结构关系图")
    st.code(get_data_structure_diagram(), language='text')

    # 5. 重调度算法流程图
    st.markdown("### 5. 完全重调度算法流程图")
    st.code(get_rescheduling_algorithm(), language='text')

    st.markdown("### 使用说明")
    st.markdown("""
    1. **在线编辑器**: 复制上述代码到 [Mermaid Live Editor](https://mermaid.live/) 中查看图形
    2. **Markdown文档**: 在Markdown文档中使用 ```mermaid 代码块包围代码
    3. **GitHub**: GitHub原生支持Mermaid图表，直接在README.md中使用即可
    4. **VS Code**: 安装Mermaid插件后可以预览图表
    5. **其他工具**: 大多数现代文档工具都支持Mermaid图表
    """)

# 添加功能介绍
st.markdown("## 系统模块说明")
st.markdown("""
### 核心算法模块
- **系统初始化**：设置环境参数和基础配置
- **问题参数设置**：定义调度问题的具体参数
- **数据结构定义**：创建必要的数据结构支持算法运行
- **混合GA-PSO初始调度**：结合遗传算法和粒子群优化的初始调度方案
- **种群初始化**：为优化算法创建初始种群
- **适应度评估**：评估每个解决方案的质量
- **多样性检测**：检查种群多样性水平
- **PSO局部搜索**：使用粒子群算法进行局部搜索（当多样性低时）
- **GA操作**：使用遗传算法操作（当多样性高时）
- **更新最优解**：记录当前最佳解决方案
- **达到终止条件?**：检查是否满足终止条件
- **输出最优调度**：返回最佳的调度方案

### 重调度模块
- **故障模拟**：模拟系统故障场景
- **完全重调度**：在故障后重新执行调度过程
- **识别已完成任务**：确认在故障前已完成的任务
- **处理正在进行任务**：处理中断的任务状态
- **提取未完成任务**：找出尚未开始或未完成的任务
- **重新优化调度**：优化剩余任务的调度方案
- **合并调度结果**：将新旧调度方案合并
- **结果可视化**：展示最终的重调度结果
""")

# 添加算法描述
st.markdown("## 算法特点")
st.markdown("""
- **混合优化策略**：结合遗传算法(GA)的全局搜索能力和粒子群优化(PSO)的局部搜索能力
- **动态适应性**：通过多样性检测动态切换优化策略
- **故障恢复机制**：内置完整故障处理流程，保证系统健壮性
- **并行处理**：核心算法和重调度模块可并行运行，提高效率
""")