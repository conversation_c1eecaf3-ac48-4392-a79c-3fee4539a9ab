import streamlit as st
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib.patches import BoxStyle, Wedge
import matplotlib as mpl

# 设置全局字体
mpl.rcParams['font.family'] = 'SimHei'  # 使用黑体显示中文
mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 定义流程图节点
def draw_flowchart():
    st.title("混合GA-PSO调度系统流程图")
    
    # 创建有向图
    G = nx.DiGraph()
    
    # 添加节点（按流程顺序）
    nodes = [
        "系统初始化", "问题参数设置", "数据结构定义", "混合GA-PSO初始调度",
        "故障模拟", "完全重调度", "识别已完成任务", "处理正在进行任务", 
        "提取未完成任务", "重新优化调度", "合并调度结果", "结果可视化",
        "种群初始化", "适应度评估", "多样性检测", "PSO局部搜索", 
        "GA操作", "更新最优解", "达到终止条件?", "输出最优调度"
    ]
    
    # 添加节点到图中
    for node in nodes:
        G.add_node(node)
    
    # 添加边（节点关系）
    edges = [
        ("系统初始化", "问题参数设置"),
        ("问题参数设置", "数据结构定义"),
        ("数据结构定义", "混合GA-PSO初始调度"),
        ("混合GA-PSO初始调度", "故障模拟"),
        ("混合GA-PSO初始调度", "种群初始化"),
        ("故障模拟", "完全重调度"),
        ("完全重调度", "识别已完成任务"),
        ("识别已完成任务", "处理正在进行任务"),
        ("处理正在进行任务", "提取未完成任务"),
        ("提取未完成任务", "重新优化调度"),
        ("重新优化调度", "合并调度结果"),
        ("合并调度结果", "结果可视化"),
        ("种群初始化", "适应度评估"),
        ("适应度评估", "多样性检测"),
        ("多样性检测", "PSO局部搜索"),
        ("多样性检测", "GA操作"),
        ("PSO局部搜索", "更新最优解"),
        ("GA操作", "更新最优解"),
        ("更新最优解", "达到终止条件?"),
        ("达到终止条件?", "输出最优调度"),
        ("达到终止条件?", "适应度评估")  # 循环连接
    ]
    
    for edge in edges:
        G.add_edge(*edge)
    
    # 创建图形布局
    plt.figure(figsize=(16, 18))
    
    # 定义自定义布局
    pos = {
        "系统初始化": (7, 15), 
        "问题参数设置": (7, 13),
        "数据结构定义": (7, 11),
        "混合GA-PSO初始调度": (7, 9),
        
        # 重调度模块
        "故障模拟": (1.5, 7),
        "完全重调度": (1.5, 5),
        "识别已完成任务": (1.5, 3),
        "处理正在进行任务": (1.5, 1),
        "提取未完成任务": (1.5, -1),
        "重新优化调度": (1.5, -3),
        "合并调度结果": (1.5, -5),
        "结果可视化": (4, -7),
        
        # 核心算法模块
        "种群初始化": (10.5, 7),
        "适应度评估": (10.5, 5),
        "多样性检测": (10.5, 3),
        "PSO局部搜索": (7.5, 1),
        "GA操作": (13.5, 1),
        "更新最优解": (10.5, -1),
        "达到终止条件?": (10.5, -3),
        "输出最优调度": (10.5, -7),
    }
    
    # 节点形状映射
    node_shapes = {
        "多样性检测": "diamond",
        "达到终止条件?": "diamond"
    }
    
    # 手动添加集群边界
    cluster1_poly = plt.Polygon([[0, 8], [0, -7.5], [3, -7.5], [3, 8]], 
                                facecolor='lightblue', alpha=0.1)
    cluster2_poly = plt.Polygon([[4.5, 10], [4.5, -10], [15, -10], [15, 10]], 
                                facecolor='lightgreen', alpha=0.1)
    
    # 创建图
    fig, ax = plt.subplots()
    ax.add_patch(cluster1_poly)
    ax.add_patch(cluster2_poly)
    
    # 添加集群标签
    plt.text(1.5, 8.5, "重调度模块", fontsize=12, weight='bold', color='navy')
    plt.text(10, 11, "核心算法模块", fontsize=12, weight='bold', color='darkgreen')
    
    # 绘制节点
    for node in G.nodes:
        shape = node_shapes.get(node, "s")  # 默认为矩形
        nx.draw_networkx_nodes(G, pos, nodelist=[node], node_shape=shape, 
                              node_size=3200, node_color='lightblue', edgecolors='black', alpha=0.8)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, width=1.5, arrowstyle='-|>', arrowsize=20)
    
    # 添加边标签
    edge_labels = {
        ("多样性检测", "PSO局部搜索"): "是",
        ("多样性检测", "GA操作"): "否",
        ("达到终止条件?", "输出最优调度"): "是",
        ("达到终止条件?", "适应度评估"): "否"
    }
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)
    
    # 绘制节点标签
    labels = {}
    for node in G.nodes:
        # 根据节点名称确定换行位置
        if "GA-PSO" in node:
            labels[node] = "混合GA-PSO\n初始调度"
        elif "完全重调度" == node:
            labels[node] = node
        else:
            labels[node] = node.replace("任务", "任务\n").replace("调度", "调度\n").replace("算法", "算法\n")
    
    # 绘制节点标签
    nx.draw_networkx_labels(G, pos, labels, font_size=8.5)
    
    # 移除坐标轴
    plt.axis('off')
    
    # 显示图形
    st.pyplot(fig)

# 显示流程图
draw_flowchart()

# 添加功能介绍
st.markdown("## 系统模块说明")
st.markdown("""
### 核心算法模块
- **系统初始化**：设置环境参数和基础配置
- **问题参数设置**：定义调度问题的具体参数
- **数据结构定义**：创建必要的数据结构支持算法运行
- **混合GA-PSO初始调度**：结合遗传算法和粒子群优化的初始调度方案
- **种群初始化**：为优化算法创建初始种群
- **适应度评估**：评估每个解决方案的质量
- **多样性检测**：检查种群多样性水平
- **PSO局部搜索**：使用粒子群算法进行局部搜索（当多样性低时）
- **GA操作**：使用遗传算法操作（当多样性高时）
- **更新最优解**：记录当前最佳解决方案
- **达到终止条件?**：检查是否满足终止条件
- **输出最优调度**：返回最佳的调度方案

### 重调度模块
- **故障模拟**：模拟系统故障场景
- **完全重调度**：在故障后重新执行调度过程
- **识别已完成任务**：确认在故障前已完成的任务
- **处理正在进行任务**：处理中断的任务状态
- **提取未完成任务**：找出尚未开始或未完成的任务
- **重新优化调度**：优化剩余任务的调度方案
- **合并调度结果**：将新旧调度方案合并
- **结果可视化**：展示最终的重调度结果
""")

# 添加算法描述
st.markdown("## 算法特点")
st.markdown("""
- **混合优化策略**：结合遗传算法(GA)的全局搜索能力和粒子群优化(PSO)的局部搜索能力
- **动态适应性**：通过多样性检测动态切换优化策略
- **故障恢复机制**：内置完整故障处理流程，保证系统健壮性
- **并行处理**：核心算法和重调度模块可并行运行，提高效率
""")