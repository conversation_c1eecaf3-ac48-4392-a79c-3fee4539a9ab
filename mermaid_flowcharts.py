#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
shuangchuan.m 项目逻辑框架流程图生成器
包含海上风电机组安装调度系统的各种流程图的 Mermaid 代码
"""

def get_overall_architecture():
    """总体逻辑架构图"""
    return """
graph TD
    A[系统初始化] --> B[问题参数设置]
    B --> C[数据结构定义]
    C --> D[混合GA-PSO初始调度]
    D --> E[故障模拟]
    E --> F[完全重调度]
    F --> G[结果可视化]
    
    subgraph "核心算法模块"
        D --> D1[种群初始化]
        D1 --> D2[适应度评估]
        D2 --> D3[多样性检测]
        D3 --> D4{多样性低?}
        D4 -->|是| D5[PSO局部搜索]
        D4 -->|否| D6[GA操作]
        D5 --> D7[更新最优解]
        D6 --> D7
        D7 --> D8{达到终止条件?}
        D8 -->|否| D2
        D8 -->|是| D9[输出最优调度]
    end
    
    subgraph "重调度模块"
        F --> F1[识别已完成任务]
        F1 --> F2[处理正在进行任务]
        F2 --> F3[提取未完成任务]
        F3 --> F4[重新优化调度]
        F4 --> F5[合并调度结果]
    end
"""

def get_hybrid_ga_pso_algorithm():
    """混合GA-PSO算法详细流程图"""
    return """
flowchart TD
    A[开始] --> B[初始化参数]
    B --> C[生成初始种群]
    C --> D[初始化PSO参数]
    D --> E[开始进化循环]
    
    E --> F[评估种群适应度]
    F --> G[更新个体最优pbest]
    G --> H[更新全局最优gbest]
    H --> I[计算种群多样性]
    
    I --> J{多样性 < 阈值?}
    
    J -->|是| K[执行PSO操作]
    K --> K1[更新粒子速度]
    K1 --> K2[更新粒子位置]
    K2 --> K3[PSO变异操作]
    K3 --> K4[评估新解]
    K4 --> K5[更新pbest和gbest]
    K5 --> L[检查终止条件]
    
    J -->|否| M[执行GA操作]
    M --> M1[精英选择]
    M1 --> M2[锦标赛选择]
    M2 --> M3[交叉操作OX]
    M3 --> M4[变异操作]
    M4 --> M5[生成新种群]
    M5 --> L
    
    L --> N{达到最大代数?}
    N -->|否| F
    N -->|是| O[输出最优解]
    O --> P[解码为调度方案]
    P --> Q[结束]
"""

def get_chromosome_decoding():
    """染色体解码为调度方案算法图"""
    return """
flowchart TD
    A[输入染色体] --> B[提取涡轮机顺序和船舶分配]
    B --> C[初始化船舶和泊位可用时间]
    C --> D[开始处理涡轮机]
    
    D --> E[获取当前涡轮机和分配船舶]
    E --> F[获取船舶属性和海况]
    F --> G{船舶类型?}
    
    G -->|单体船| H1[组装级别=0]
    G -->|双体船| H2[组装级别=2]
    
    H1 --> I[分配泊位]
    H2 --> I
    
    I --> J[计算装载时间]
    J --> K{需要船上组装?}
    
    K -->|是| L[添加船上组装任务]
    K -->|否| M[计算前往风场时间]
    L --> M
    
    M --> N[添加航行任务]
    N --> O{组装级别?}
    
    O -->|0-无组装| P[执行所有常规工序]
    O -->|2-完全组装| Q[执行最终安装工序]
    
    P --> R[应用海况影响]
    Q --> R
    
    R --> S[添加返回港口任务]
    S --> T[更新船舶可用时间]
    T --> U{还有涡轮机?}
    
    U -->|是| D
    U -->|否| V[输出完整调度]
"""

def get_data_structure_diagram():
    """核心数据结构关系图"""
    return """
erDiagram
    TURBINES {
        int id
        array processes
        int assembly_level
        struct ship_assembly_time
        int sea_condition_index
        float preassembly_time
        bool is_preassembled
    }
    
    VESSELS {
        int id
        int type
        float speed
        int capacity
        float loading_time
        float deck_space
        float stability
        float sea_efficiency
        int max_assembly_level
    }
    
    BERTHS {
        int id
    }
    
    SCHEDULE {
        array turbine_id
        array vessel_id
        array start_time
        array end_time
        array process_id
        array berth_id
        array sea_condition
        array assembly_level
    }
    
    SEA_CONDITIONS {
        array wave_height
        array wind_speed
        array current_speed
    }
    
    CHROMOSOME {
        array turbine_order
        array vessel_assignment
    }
    
    TURBINES ||--o{ SCHEDULE : "安装任务"
    VESSELS ||--o{ SCHEDULE : "执行任务"
    BERTHS ||--o{ SCHEDULE : "占用泊位"
    SEA_CONDITIONS ||--o{ TURBINES : "影响位置"
    CHROMOSOME ||--|| SCHEDULE : "解码生成"
"""

def get_rescheduling_algorithm():
    """完全重调度算法流程图"""
    return """
flowchart TD
    A[故障发生] --> B[输入原始调度和故障信息]
    B --> C[分析任务状态]
    
    C --> D[识别已完成任务]
    C --> E[识别正在进行任务]
    C --> F[识别未开始任务]
    
    D --> G[保留已完成任务]
    E --> H[调整正在进行任务]
    F --> I[提取未完成涡轮机]
    
    G --> J[创建新调度基础]
    H --> K[添加维修任务]
    K --> L[处理故障船舶延迟]
    
    I --> M[构建简化问题]
    M --> N[计算船舶可用时间]
    N --> O[调用优化算法]
    
    O --> P[生成剩余任务调度]
    
    J --> Q[合并调度结果]
    L --> Q
    P --> Q
    
    Q --> R[输出完整重调度方案]
    
    subgraph "任务状态分类"
        C1[end_time ≤ 故障时间] --> D
        C2[start_time < 故障时间 < end_time] --> E  
        C3[start_time ≥ 故障时间] --> F
    end
"""

def print_all_mermaid_diagrams():
    """打印所有Mermaid流程图代码"""
    print("=" * 80)
    print("shuangchuan.m 项目逻辑框架 - Mermaid 流程图代码")
    print("=" * 80)
    
    print("\n1. 总体逻辑架构图:")
    print("-" * 50)
    print(get_overall_architecture())
    
    print("\n2. 混合GA-PSO算法详细流程图:")
    print("-" * 50)
    print(get_hybrid_ga_pso_algorithm())
    
    print("\n3. 染色体解码为调度方案算法图:")
    print("-" * 50)
    print(get_chromosome_decoding())
    
    print("\n4. 核心数据结构关系图:")
    print("-" * 50)
    print(get_data_structure_diagram())
    
    print("\n5. 完全重调度算法流程图:")
    print("-" * 50)
    print(get_rescheduling_algorithm())
    
    print("\n" + "=" * 80)
    print("使用说明:")
    print("1. 复制上述Mermaid代码到支持Mermaid的编辑器中")
    print("2. 或者使用在线Mermaid编辑器: https://mermaid.live/")
    print("3. 或者在Markdown文档中使用 ```mermaid 代码块")
    print("=" * 80)

if __name__ == "__main__":
    print_all_mermaid_diagrams()
